{% extends "base.html" %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="staff-container">
    <h1>User Management</h1>

    <!-- Flash Messages - Using Standardized Notification System -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div id="flash-messages-container">
                {% for category, message in messages %}
                    <div class="message {{ 'error' if category == 'error' else 'success' if category == 'success' else 'warning' if category == 'warning' else 'info' }}">
                        <i class="fas fa-{{ 'exclamation-circle' if category == 'error' else 'check-circle' if category == 'success' else 'exclamation-triangle' if category == 'warning' else 'info-circle' }}"></i>
                        <span class="message-text">{{ message }}</span>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Search and Filter Section -->
    <div class="search-filter-section">
        <div class="filters-wrapper">
            <div class="search-box">
                <input type="text" id="searchInput" class="form-input" placeholder="Search users..." onkeyup="filterUsers()">
            </div>
            <div class="filter-item">
                <label for="roleFilter" class="visually-hidden">Filter by Role</label>
                <select id="roleFilter" class="filter-select" onchange="filterUsers()" aria-label="Filter by Role">
                    <option value="">All Roles</option>
                    {% for role in roles %}
                    <option value="{{ role.name }}">{{ role.display_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="filter-item">
                <label for="statusFilter" class="visually-hidden">Filter by Status</label>
                <select id="statusFilter" class="filter-select" onchange="filterUsers()" aria-label="Filter by Status">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="filter-item">
                <button type="button" onclick="clearFilters()" class="btn btn-secondary">Clear</button>
            </div>
        </div>
    </div>    <!-- Add User Form -->
    <div class="inventory-actions">
        <div class="inventory-search">
            <form method="post" class="add-user-form" autocomplete="off">
                <div class="form-group">
                    <label for="username" class="form-label">Username:</label>
                    <input type="text" id="username" name="username" required class="form-input" autocomplete="new-username" placeholder="Enter username">
                </div>
                <div class="form-group">
                    <label for="full_name" class="form-label">Full Name:</label>
                    <input type="text" id="full_name" name="full_name" class="form-input" autocomplete="off" placeholder="Enter full name (optional)">
                </div>
                <div class="form-group">
                    <label for="email" class="form-label">Email:</label>
                    <input type="email" id="email" name="email" class="form-input" autocomplete="off" placeholder="Enter email (optional)">
                </div>
                <div class="form-group wide">
                    <label for="password" class="form-label">Password:</label>
                    <div class="form-row">
                        <input type="password" id="password" name="password" required class="form-input" autocomplete="new-password" placeholder="Enter password (min 8 characters)">
                        <button type="button" class="btn btn-secondary generate-password-btn" data-action="generate-form-password" title="Generate Password">Gen</button>
                    </div>
                </div>
                <div class="form-group narrow">
                    <label for="role" class="form-label">Role:</label>
                    <select id="role" name="role" required class="form-select">
                        {% for role in roles %}
                        <option value="{{ role.name }}">{{ role.display_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group action-group">
                    <label class="form-label visually-hidden">Action:</label>
                    <button type="submit" class="btn btn-primary">Add User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="inventory-list">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #aaa; font-weight: 400;">
            <thead>
                <tr>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">ID</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Username</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Full Name</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Email</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Role</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Last Login</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #888; background-color: #f5f5f5;">Status</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #888; background-color: #f5f5f5;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">{{ user.id }}</td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="username-display">{{ user.username }}</span>
                        <input type="text" class="username-edit" value="{{ user.username }}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                    </td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="fullname-display">{{ user.full_name or '-' }}</span>
                        <input type="text" class="fullname-edit" value="{{ user.full_name or '' }}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                    </td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="email-display">{{ user.email or '-' }}</span>
                        <input type="email" class="email-edit" value="{{ user.email or '' }}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                    </td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="role-display">
                            {% set role_colors = {
                                'super_admin': '#dc3545',
                                'admin': '#fd7e14',
                                'manager': '#6f42c1',
                                'staff': '#28a745',
                                'sales': '#17a2b8',
                                'clerk': '#6c757d',
                                'cashier': '#ffc107'
                            } %}
                            {% set role_obj = roles|selectattr('name', 'equalto', user.role)|first %}
                            <span class="role-badge role-{{ user.role.replace('_', '-') }}" data-role="{{ user.role }}">
                                {{ role_obj.display_name if role_obj else user.role|title }}
                            </span>
                        </span>
                        <select class="role-edit" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                            {% for role in roles %}
                            <option value="{{ role.name }}" {% if user.role == role.name %}selected{% endif %}>{{ role.display_name }}</option>
                            {% endfor %}
                        </select>
                    </td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        {% if user.last_login %}
                            {{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else '-' }}
                        {% else %}
                            <span style="color: #6c757d;">Never</span>
                        {% endif %}
                    </td>
                    <td style="padding: 12px; text-align: center; border-bottom: 1px solid #888;">
                        {% if user.is_active %}
                            <span style="background-color: #28a745; color: white; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Active</span>
                        {% else %}
                            <span style="background-color: #dc3545; color: white; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Inactive</span>
                        {% endif %}
                        {% if user.force_password_change %}
                            <br><span style="background-color: #ffc107; color: black; padding: 2px 6px; border-radius: 8px; font-size: 10px; margin-top: 2px; display: inline-block;">Pwd Reset Required</span>
                        {% endif %}
                    </td>
                    <td style="padding: 12px; text-align: center; border-bottom: 1px solid #888;">
                        <div class="action-buttons" data-user-id="{{ user.id }}" data-username="{{ user.username }}" data-is-active="{{ user.is_active }}">
                            <button class="action-btn btn-warning edit-btn" data-action="edit">Edit</button>
                            <button class="action-btn btn-success save-btn" data-action="save" style="display: none;">Save</button>
                            <button class="action-btn btn-secondary cancel-btn" data-action="cancel" style="display: none;">Cancel</button>
                            <button class="action-btn btn-info" data-action="reset-password" title="Reset Password">Reset Pwd</button>
                            {% if user.id != session.user_id %}
                            <button class="action-btn btn-purple" data-action="force-password" title="Force Password Change">Force Pwd</button>
                            <button class="action-btn {% if user.is_active %}btn-warning{% else %}btn-success{% endif %}" data-action="toggle-status" title="{% if user.is_active %}Deactivate{% else %}Activate{% endif %} User">{% if user.is_active %}Deactivate{% else %}Activate{% endif %}</button>
                            <button class="action-btn btn-danger" data-action="delete">Delete</button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Message Container for Notifications -->
<div id="message-container"></div>

{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff_notifications.css') }}">
<style>
    .staff-container {
        width: 100%;
        margin: 0 auto;
        padding: 20px;
    }

    .inventory-actions {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        margin: 20px 0;
        gap: 15px;
    }

    .inventory-search {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;
        max-width: 1000px;
    }

    .inventory-list table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #aaa;
        font-weight: 400;
    }

    .inventory-list th,
    .inventory-list td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #888;
    }

    .inventory-list th {
        background-color: #f5f5f5;
        font-weight: bold;
    }

    .inventory-list tbody tr:hover {
        background-color: #f8f9fa;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
        justify-content: center;
        align-items: center;
    }

    .action-buttons button {
        transition: background-color 0.2s;
    }

    .action-buttons button:hover {
        opacity: 0.8;
    }

    /* Form and Filter Styles */
    .filter-item {
        flex: 0 0 auto;
    }

    .filter-select,
    .form-select,
    .form-input {
        padding: 8px 12px;
        border: 1px solid #ccc;
        border-radius: 5px;
        width: 100%;
    }

    .btn {
        padding: 8px 12px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        color: white;
    }

    .btn-secondary {
        background-color: #6c757d;
    }

    .btn-primary {
        background-color: #007bff;
    }

    .btn-warning {
        background-color: #ffc107;
        color: #212529;
    }

    .btn-success {
        background-color: #28a745;
    }

    .btn-danger {
        background-color: #dc3545;
    }

    .btn-info {
        background-color: #17a2b8;
    }

    .btn-purple {
        background-color: #6f42c1;
    }

    .form-group {
        flex: 1;
        min-width: 140px;
    }

    .form-group.wide {
        flex: 1.2;
        min-width: 180px;
    }

    .form-group.narrow {
        flex: 0.8;
        min-width: 110px;
    }

    .form-label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }

    .form-row {
        display: flex;
        gap: 5px;
    }

    .visually-hidden {
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0,0,0,0) !important;
        white-space: nowrap !important;
        border: 0 !important;
    }

    .search-filter-section {
        margin: 20px 0;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }

    .filters-wrapper {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
    }

    .search-box {
        flex: 1;
        min-width: 200px;
    }

    .add-user-form {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: end;
        width: 100%;
    }

    .action-group {
        flex: 0 0 auto;
        margin-left: 10px;
    }

    .action-group .btn {
        min-width: 100px;
        padding: 8px 16px;
    }

    /* Table Styles */
    .table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #aaa;
        font-weight: 400;
    }

    .table th,
    .table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #888;
    }

    .table th {
        background-color: #f5f5f5;
    }

    .table td.actions {
        text-align: center;
    }

    .status-badge {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 11px;
        color: white;
    }

    .status-badge.active {
        background-color: #28a745;
    }

    .status-badge.inactive {
        background-color: #dc3545;
    }

    .status-badge.pwd-reset {
        background-color: #ffc107;
        color: black;
        margin-top: 2px;
        font-size: 10px;
    }

    .role-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        color: white;
    }

    .role-badge.super-admin {
        background-color: #dc3545;
    }

    .role-badge.admin {
        background-color: #fd7e14;
    }

    .role-badge.manager {
        background-color: #6f42c1;
    }

    .role-badge.staff {
        background-color: #28a745;
    }

    .role-badge.sales {
        background-color: #17a2b8;
    }

    .role-badge.clerk {
        background-color: #6c757d;
    }

    .role-badge.cashier {
        background-color: #ffc107;
    }

    .edit-field {
        padding: 4px;
        border: 1px solid #ccc;
        border-radius: 3px;
        display: none;
    }

    .action-btn {
        font-size: 12px;
        margin-right: 5px;
        padding: 4px 8px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        color: white;
    }

    .never-login {
        color: #6c757d;
    }

    /* Role badge colors */
    .role-badge.role-super-admin {
        background-color: #dc3545;
    }

    .role-badge.role-admin {
        background-color: #fd7e14;
    }

    .role-badge.role-manager {
        background-color: #6f42c1;
    }

    .role-badge.role-staff {
        background-color: #28a745;
    }

    .role-badge.role-sales {
        background-color: #17a2b8;
    }

    .role-badge.role-clerk {
        background-color: #6c757d;
    }

    .role-badge.role-cashier {
        background-color: #ffc107;
    }

    /* Flash messages - Using standardized notification styling */
    #flash-messages-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        max-width: 400px;
    }

    #flash-messages-container .message {
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/staff_messages.js') }}"></script>
<script>
// Event delegation for action buttons and modal buttons
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide flash messages after 5 seconds
    const flashMessages = document.querySelectorAll('#flash-messages-container .message');
    flashMessages.forEach(message => {
        setTimeout(() => {
            message.classList.add('fade-out');
            setTimeout(() => {
                if (message.parentNode) {
                    message.remove();
                }
            }, 300); // Match CSS animation duration
        }, 5000); // 5 seconds delay
    });
    // Clear form fields on page load to prevent browser auto-fill
    const addUserForm = document.querySelector('.add-user-form');
    if (addUserForm) {
        // Function to clear form fields
        function clearFormFields() {
            addUserForm.reset();

            // Force clear specific fields that might be auto-filled
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');
            const emailField = document.getElementById('email');
            const fullNameField = document.getElementById('full_name');

            if (usernameField) usernameField.value = '';
            if (passwordField) passwordField.value = '';
            if (emailField) emailField.value = '';
            if (fullNameField) fullNameField.value = '';
        }

        // Clear immediately
        clearFormFields();

        // Clear again after a short delay to override any browser auto-fill
        setTimeout(clearFormFields, 100);
        setTimeout(clearFormFields, 500);
    }

    // Handle form submission for adding new users
    if (addUserForm) {
        addUserForm.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent default form submission

            const formData = new FormData(this);
            const username = formData.get('username').trim();
            const password = formData.get('password').trim();
            const role = formData.get('role');
            const email = formData.get('email').trim() || null;
            const full_name = formData.get('full_name').trim() || null;

            if (!username || !password) {
                showMessage('Username and password are required', 'error');
                return;
            }

            if (password.length < 8) {
                showMessage('Password must be at least 8 characters', 'error');
                return;
            }

            console.log('Submitting user creation request...');
            console.log('Data:', { username, password: '***', role, email, full_name });

            // Submit form data via fetch
            fetch('/auth/staff/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: new URLSearchParams({
                    username: username,
                    password: password,
                    role: role,
                    email: email || '',
                    full_name: full_name || '',
                    ajax: 'true'
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers.get('content-type'));
                console.log('Response URL:', response.url);

                if (!response.ok) {
                    console.error('Response not OK:', response.status, response.statusText);
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json();
                } else {
                    console.error('Expected JSON but got:', contentType);
                    // If not JSON, it might be a redirect response (HTML)
                    throw new Error('Expected JSON response but got HTML - user may have been created but page needs refresh');
                }
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    showMessage(data.message, 'success');
                    // Reset form
                    addUserForm.reset();
                    // Add new user to table dynamically
                    addUserToTable(data.user);
                } else {
                    showMessage(data.error || 'Error creating user. Please try again.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (error.message.includes('user may have been created')) {
                    showMessage('User created successfully! Please refresh the page to see the new user.', 'success');
                    // Reset form since user was likely created
                    addUserForm.reset();
                } else {
                    showMessage('Error creating user. Please try again.', 'error');
                }
            });
        });
    }

    document.addEventListener('click', function(e) {
        if (e.target.matches('.action-btn')) {
            const actionButtons = e.target.closest('.action-buttons');
            const userId = actionButtons.dataset.userId;
            const username = actionButtons.dataset.username;
            const isActive = actionButtons.dataset.isActive === 'true';
            const action = e.target.dataset.action;

            switch(action) {
                case 'edit':
                    editUser(userId);
                    break;
                case 'save':
                    saveUser(userId);
                    break;
                case 'cancel':
                    cancelEdit(userId);
                    break;
                case 'reset-password':
                    resetPassword(userId, username);
                    break;
                case 'force-password':
                    forcePasswordChange(userId, username);
                    break;
                case 'toggle-status':
                    toggleUserStatus(userId, username, isActive);
                    break;
                case 'delete':
                    deleteUser(userId, username);
                    break;
            }
        }

        // Handle modal button clicks
        if (e.target.matches('.modal-btn')) {
            const action = e.target.dataset.action;
            const userId = e.target.dataset.userId;
            const username = e.target.dataset.username;

            switch(action) {
                case 'generate-password':
                    generateModalPassword();
                    break;
                case 'close-password-reset':
                    closePasswordResetModal();
                    break;
                case 'confirm-password-reset':
                    confirmPasswordReset(userId, username);
                    break;
                case 'close-password-result':
                    closePasswordResultModal();
                    break;
                case 'copy-password':
                    copyPassword();
                    break;
                case 'close-force-password':
                    closeForcePasswordModal();
                    break;
                case 'confirm-force-password':
                    confirmForcePasswordChange(userId, username);
                    break;
            }
        }

        // Handle form button clicks
        if (e.target.matches('.generate-password-btn')) {
            const action = e.target.dataset.action;
            if (action === 'generate-form-password') {
                generatePassword();
            }
        }
    });
});
function editUser(userId) {
    const row = findRowByUserId(userId);
    if (!row) {
        console.error('Could not find row for user ID:', userId);
        return;
    }

    // Hide display elements and show edit elements
    row.querySelector('.username-display').style.display = 'none';
    row.querySelector('.username-edit').style.display = 'inline';
    row.querySelector('.fullname-display').style.display = 'none';
    row.querySelector('.fullname-edit').style.display = 'inline';
    row.querySelector('.email-display').style.display = 'none';
    row.querySelector('.email-edit').style.display = 'inline';
    row.querySelector('.role-display').style.display = 'none';
    row.querySelector('.role-edit').style.display = 'inline';

    // Hide edit button, show save/cancel buttons
    row.querySelector('[data-action="edit"]').style.display = 'none';
    row.querySelector('[data-action="save"]').style.display = 'inline';
    row.querySelector('[data-action="cancel"]').style.display = 'inline';
}

function cancelEdit(userId) {
    const row = findRowByUserId(userId);
    if (!row) {
        console.error('Could not find row for user ID:', userId);
        return;
    }

    // Show display elements and hide edit elements
    row.querySelector('.username-display').style.display = 'inline';
    row.querySelector('.username-edit').style.display = 'none';
    row.querySelector('.fullname-display').style.display = 'inline';
    row.querySelector('.fullname-edit').style.display = 'none';
    row.querySelector('.email-display').style.display = 'inline';
    row.querySelector('.email-edit').style.display = 'none';
    row.querySelector('.role-display').style.display = 'inline';
    row.querySelector('.role-edit').style.display = 'none';

    // Show edit button, hide save/cancel buttons
    row.querySelector('[data-action="edit"]').style.display = 'inline';
    row.querySelector('[data-action="save"]').style.display = 'none';
    row.querySelector('[data-action="cancel"]').style.display = 'none';

    // Reset values
    const originalUsername = row.querySelector('.username-display').textContent;
    const originalFullname = row.querySelector('.fullname-display').textContent;
    const originalEmail = row.querySelector('.email-display').textContent;
    row.querySelector('.username-edit').value = originalUsername;
    row.querySelector('.fullname-edit').value = originalFullname === '-' ? '' : originalFullname;
    row.querySelector('.email-edit').value = originalEmail === '-' ? '' : originalEmail;
}

function saveUser(userId) {
    const row = findRowByUserId(userId);
    if (!row) {
        console.error('Could not find row for user ID:', userId);
        return;
    }

    const username = row.querySelector('.username-edit').value.trim();
    const fullName = row.querySelector('.fullname-edit').value.trim();
    const email = row.querySelector('.email-edit').value.trim();
    const role = row.querySelector('.role-edit').value;

    if (!username) {
        showMessage('Username cannot be empty', 'error');
        return;
    }

    fetch(`/auth/api/staff/users/${userId}/update`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: username,
            full_name: fullName || null,
            email: email || null,
            role: role
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // Update display values
            row.querySelector('.username-display').textContent = username;
            row.querySelector('.fullname-display').textContent = fullName || '-';
            row.querySelector('.email-display').textContent = email || '-';

            // Update role badge
            const roleDisplay = row.querySelector('.role-display span');
            if (role === 'super_admin') {
                roleDisplay.textContent = 'Super Admin';
                roleDisplay.style.backgroundColor = '#dc3545';
            } else if (role === 'admin') {
                roleDisplay.textContent = 'Admin';
                roleDisplay.style.backgroundColor = '#fd7e14';
            } else {
                roleDisplay.textContent = 'Staff';
                roleDisplay.style.backgroundColor = '#28a745';
            }

            cancelEdit(userId);
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error updating user', 'error');
    });
}

function findRowByUserId(userId) {
    // Find the row by looking for action-buttons div with the userId in data-user-id attribute
    const actionButtons = document.querySelector('.action-buttons[data-user-id="' + userId + '"]');
    if (actionButtons) {
        return actionButtons.closest('tr');
    }
    return null;
}

async function deleteUser(userId, username) {
    const confirmed = await showDeleteConfirmation(
        'Delete User',
        `Are you sure you want to delete user "${username}"? This action cannot be undone.`
    );

    if (confirmed) {
        fetch(`/auth/api/staff/users/${userId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                // Remove the row from table
                const row = findRowByUserId(userId);
                if (row) {
                    row.remove();
                }
            } else {
                showMessage(data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Error deleting user', 'error');
        });
    }
}

function resetPassword(userId, username) {
    showPasswordResetModal(userId, username);
}

function showPasswordResetModal(userId, username) {
    // Create modal HTML
    const modalHTML = `
        <div id="passwordResetModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); max-width: 500px; width: 90%;">
                <div style="text-align: center; margin-bottom: 1.5rem;">
                    <i class="fas fa-key" style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 1rem;"></i>
                    <h3 style="color: #333; margin: 0 0 0.5rem 0;">Reset Password</h3>
                    <p style="color: #666; margin: 0;">Reset password for user: <strong>${username}</strong></p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <label for="newPasswordInput" style="display: block; margin-bottom: 0.5rem; color: #333; font-weight: 500;">New Password:</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" id="newPasswordInput" placeholder="Leave empty to generate temporary password" style="flex: 1; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; font-size: 1rem;">
                        <button type="button" class="modal-btn" data-action="generate-password" style="padding: 0.75rem 1rem; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; white-space: nowrap;">Generate</button>
                    </div>
                    <small style="color: #666; font-size: 0.85rem; margin-top: 0.5rem; display: block;">Leave empty to auto-generate a secure temporary password</small>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="modal-btn" data-action="close-password-reset" style="padding: 0.75rem 1.5rem; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">Cancel</button>
                    <button type="button" class="modal-btn" data-action="confirm-password-reset" data-user-id="${userId}" data-username="${username}" style="padding: 0.75rem 1.5rem; background-color: #17a2b8; color: white; border: none; border-radius: 5px; cursor: pointer;">Reset Password</button>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Focus on input
    document.getElementById('newPasswordInput').focus();

    // Close modal when clicking outside
    document.getElementById('passwordResetModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closePasswordResetModal();
        }
    });
}

function generateModalPassword() {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
    let password = '';
    for (let i = 0; i < 12; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('newPasswordInput').value = password;
}

function closePasswordResetModal() {
    const modal = document.getElementById('passwordResetModal');
    if (modal) {
        modal.remove();
    }
}

function confirmPasswordReset(userId, username) {
    const newPassword = document.getElementById('newPasswordInput').value.trim();

    fetch(`/auth/api/staff/users/${userId}/reset-password`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            new_password: newPassword
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closePasswordResetModal();
            showMessage(data.message, 'success');
            if (data.temporary_password) {
                showPasswordResultModal(data.temporary_password, username);
            }
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error resetting password', 'error');
    });
}

function showPasswordResultModal(password, username) {
    const modalHTML = `
        <div id="passwordResultModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); max-width: 500px; width: 90%;">
                <div style="text-align: center; margin-bottom: 1.5rem;">
                    <i class="fas fa-check-circle" style="font-size: 2.5rem; color: #28a745; margin-bottom: 1rem;"></i>
                    <h3 style="color: #333; margin: 0 0 0.5rem 0;">Password Reset Successful</h3>
                    <p style="color: #666; margin: 0;">Temporary password for user: <strong>${username}</strong></p>
                </div>

                <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; margin-bottom: 1.5rem; border-left: 4px solid #28a745;">
                    <label style="display: block; margin-bottom: 0.5rem; color: #333; font-weight: 500;">Temporary Password:</label>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="tempPasswordDisplay" value="${password}" readonly style="flex: 1; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; font-size: 1rem; font-family: monospace; background: white;">
                        <button type="button" class="modal-btn" data-action="copy-password" style="padding: 0.75rem 1rem; background-color: #17a2b8; color: white; border: none; border-radius: 5px; cursor: pointer;">Copy</button>
                    </div>
                    <small style="color: #666; font-size: 0.85rem; margin-top: 0.5rem; display: block;">⚠️ Please save this password securely and share it with the user through a secure channel.</small>
                </div>

                <div style="text-align: center;">
                    <button type="button" class="modal-btn" data-action="close-password-result" style="padding: 0.75rem 1.5rem; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">Got It</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function copyPassword() {
    const passwordInput = document.getElementById('tempPasswordDisplay');
    passwordInput.select();
    navigator.clipboard.writeText(passwordInput.value).then(() => {
        showMessage('Password copied to clipboard!', 'success');
    }).catch(() => {
        // Fallback for older browsers
        document.execCommand('copy');
        showMessage('Password copied to clipboard!', 'success');
    });
}

function closePasswordResultModal() {
    const modal = document.getElementById('passwordResultModal');
    if (modal) {
        modal.remove();
    }
}

function generatePassword() {
    fetch('/auth/api/staff/users/generate-password')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('password').value = data.password;
            document.getElementById('password').type = 'text'; // Show generated password
            showMessage('Password generated successfully', 'success');
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error generating password', 'error');
    });
}

function filterUsers() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const roleFilter = document.getElementById('roleFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const username = row.cells[1].textContent.toLowerCase();
        const fullName = row.cells[2].textContent.toLowerCase();
        const email = row.cells[3].textContent.toLowerCase();
        const role = row.cells[4].textContent.toLowerCase();
        const status = row.cells[6].textContent.toLowerCase();

        // Check search term
        const matchesSearch = !searchTerm ||
            username.includes(searchTerm) ||
            fullName.includes(searchTerm) ||
            email.includes(searchTerm);

        // Check role filter
        const matchesRole = !roleFilter || role.includes(roleFilter.toLowerCase());

        // Check status filter
        const matchesStatus = !statusFilter ||
            (statusFilter === 'active' && status.includes('active')) ||
            (statusFilter === 'inactive' && status.includes('inactive'));

        // Show/hide row based on all filters
        if (matchesSearch && matchesRole && matchesStatus) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('roleFilter').value = '';
    document.getElementById('statusFilter').value = '';
    filterUsers();
}

function addUserToTable(user) {
    const tbody = document.querySelector('tbody');

    // Get role colors mapping
    const roleColors = {
        'super_admin': '#dc3545',
        'admin': '#fd7e14',
        'manager': '#6f42c1',
        'staff': '#28a745',
        'sales': '#17a2b8',
        'clerk': '#6c757d',
        'cashier': '#ffc107'
    };

    // Create new row
    const newRow = document.createElement('tr');
    const roleColor = roleColors[user.role] || '#28a745';
    const roleCssClass = user.role.replace('_', '-');

    newRow.innerHTML = `
        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">${user.id}</td>
        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
            <span class="username-display">${user.username}</span>
            <input type="text" class="username-edit" value="${user.username}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
        </td>
        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
            <span class="fullname-display">${user.full_name || '-'}</span>
            <input type="text" class="fullname-edit" value="${user.full_name || ''}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
        </td>
        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
            <span class="email-display">${user.email || '-'}</span>
            <input type="email" class="email-edit" value="${user.email || ''}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
        </td>
        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
            <span class="role-display">
                <span class="role-badge role-${roleCssClass}" data-role="${user.role}" style="background-color: ${roleColor}; display: inline-block; padding: 4px 8px; border-radius: 12px; font-size: 12px; color: white;">
                    ${user.role_display_name}
                </span>
            </span>
            <select class="role-edit" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                <!-- Role options will be populated dynamically -->
            </select>
        </td>
        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
            <span style="color: #6c757d;">Never</span>
        </td>
        <td style="padding: 12px; text-align: center; border-bottom: 1px solid #888;">
            <span style="background-color: #28a745; color: white; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Active</span>
        </td>
        <td style="padding: 12px; text-align: center; border-bottom: 1px solid #888;">
            <div class="action-buttons" data-user-id="${user.id}" data-username="${user.username}" data-is-active="true">
                <button class="action-btn btn-warning edit-btn" data-action="edit">Edit</button>
                <button class="action-btn btn-success save-btn" data-action="save" style="display: none;">Save</button>
                <button class="action-btn btn-secondary cancel-btn" data-action="cancel" style="display: none;">Cancel</button>
                <button class="action-btn btn-info" data-action="reset-password" title="Reset Password">Reset Pwd</button>
                <button class="action-btn btn-purple" data-action="force-password" title="Force Password Change">Force Pwd</button>
                <button class="action-btn btn-success" data-action="toggle-status" title="Deactivate User">Deactivate</button>
                <button class="action-btn btn-danger" data-action="delete">Delete</button>
            </div>
        </td>
    `;

    // Populate role options for the edit dropdown
    const roleSelect = newRow.querySelector('.role-edit');
    const existingRoleSelect = document.querySelector('tbody .role-edit');
    if (existingRoleSelect) {
        roleSelect.innerHTML = existingRoleSelect.innerHTML;
        // Set the correct selected option
        roleSelect.value = user.role;
    } else {
        // Fallback: create basic role options
        roleSelect.innerHTML = `
            <option value="staff">Staff</option>
            <option value="admin">Admin</option>
            <option value="manager">Manager</option>
        `;
        roleSelect.value = user.role;
    }

    // Add the new row to the table
    tbody.appendChild(newRow);

    // Scroll to the new row
    newRow.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Highlight the new row briefly
    newRow.style.backgroundColor = '#e8f5e8';
    setTimeout(() => {
        newRow.style.backgroundColor = '';
    }, 2000);
}

async function toggleUserStatus(userId, username, currentStatus) {
    console.log('toggleUserStatus called with:', { userId, username, currentStatus });

    const newStatus = !currentStatus;
    const action = newStatus ? 'activate' : 'deactivate';

    const confirmed = await showStatusConfirmation(
        `${action.charAt(0).toUpperCase() + action.slice(1)} User`,
        `Are you sure you want to ${action} user "${username}"?`
    );

    if (confirmed) {
        console.log('Sending request to:', `/auth/api/staff/users/${userId}/toggle-status`);
        console.log('Request body:', { is_active: newStatus });

        fetch(`/auth/api/staff/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                is_active: newStatus
            })
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                showMessage(data.message, 'success');
                // Refresh the page to update the UI
                window.location.reload();
            } else {
                showMessage(data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Error updating user status', 'error');
        });
    }
}

function forcePasswordChange(userId, username) {
    showForcePasswordChangeModal(userId, username);
}

function showForcePasswordChangeModal(userId, username) {
    const modalHTML = `
        <div id="forcePasswordModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); max-width: 500px; width: 90%;">
                <div style="text-align: center; margin-bottom: 1.5rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2.5rem; color: #ffc107; margin-bottom: 1rem;"></i>
                    <h3 style="color: #333; margin: 0 0 0.5rem 0;">Force Password Change</h3>
                    <p style="color: #666; margin: 0;">User: <strong>${username}</strong></p>
                </div>

                <div style="background: #fff3cd; padding: 1rem; border-radius: 5px; margin-bottom: 1.5rem; border-left: 4px solid #ffc107;">
                    <p style="margin: 0; color: #856404; font-size: 0.95rem;">
                        <strong>⚠️ This action will:</strong><br>
                        • Require the user to change their password on next login<br>
                        • Prevent access until password is changed<br>
                        • Cannot be undone automatically
                    </p>
                </div>

                <p style="color: #666; margin-bottom: 1.5rem; text-align: center;">
                    Are you sure you want to force a password change for this user?
                </p>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="modal-btn" data-action="close-force-password" style="padding: 0.75rem 1.5rem; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">Cancel</button>
                    <button type="button" class="modal-btn" data-action="confirm-force-password" data-user-id="${userId}" data-username="${username}" style="padding: 0.75rem 1.5rem; background-color: #ffc107; color: #212529; border: none; border-radius: 5px; cursor: pointer; font-weight: 500;">Force Change</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Close modal when clicking outside
    document.getElementById('forcePasswordModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeForcePasswordModal();
        }
    });
}

function closeForcePasswordModal() {
    const modal = document.getElementById('forcePasswordModal');
    if (modal) {
        modal.remove();
    }
}

function confirmForcePasswordChange(userId, username) {
    fetch(`/auth/api/staff/users/${userId}/force-password-change`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeForcePasswordModal();
            showMessage(data.message, 'success');
            // Refresh the page to update the UI
            window.location.reload();
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error forcing password change', 'error');
    });
}

function showStatusConfirmation(title, message) {
    return new Promise((resolve) => {
        // Create modal HTML with modern styling
        const modalHTML = `
            <div id="statusConfirmModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); max-width: 400px; width: 90%; text-align: center;">
                    <div style="margin-bottom: 1.5rem;">
                        <i class="fas fa-question-circle" style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 1rem;"></i>
                        <h3 style="color: #333; margin: 0 0 0.5rem 0; font-size: 1.25rem;">${title}</h3>
                        <p style="color: #666; margin: 0; font-size: 0.95rem;">${message}</p>
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button type="button" id="statusConfirmCancel" style="padding: 0.75rem 1.5rem; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 0.9rem; min-width: 80px;">Cancel</button>
                        <button type="button" id="statusConfirmOK" style="padding: 0.75rem 1.5rem; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 0.9rem; min-width: 80px; font-weight: 500;">OK</button>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        const modal = document.getElementById('statusConfirmModal');
        const okButton = document.getElementById('statusConfirmOK');
        const cancelButton = document.getElementById('statusConfirmCancel');

        // Handle OK button click
        okButton.addEventListener('click', function() {
            modal.remove();
            resolve(true);
        });

        // Handle Cancel button click
        cancelButton.addEventListener('click', function() {
            modal.remove();
            resolve(false);
        });

        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
                resolve(false);
            }
        });

        // Focus on OK button for keyboard accessibility
        okButton.focus();

        // Handle Escape key
        const handleEscape = function(e) {
            if (e.key === 'Escape') {
                modal.remove();
                resolve(false);
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
    });
}
</script>
{% endblock %}
